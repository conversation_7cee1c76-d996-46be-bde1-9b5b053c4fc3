import { Debug } from '@thrift/common/engines/Debug'
import { Server, ServerMethod } from '@thrift/common/engines/Server'
import { RouteRegistry } from '@thrift/common/engines/Server/RouteRegistry'

const register = (method: ServerMethod, endpoint: string) => {
  return (
    target: object,
    propertyKey: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    descriptor: TypedPropertyDescriptor<any>,
  ) => {
    // Extract file and class information for conflict detection
    const fileName = target.constructor.name || 'Unknown'
    const className = target.constructor.name || 'Unknown'
    const methodName = propertyKey

    // Register route with conflict detection
    RouteRegistry.registerRoute(method, endpoint, fileName, className, methodName)

    Server.router[method]({
      endpoint,
      callback: (props) => descriptor.value(props),
    })

    Debug.info({
      message: `"${method}:${endpoint}" has been routing successfully`,
    })
  }
}

export const Get = (endpoint: string) => register(ServerMethod.GET, endpoint)

export const Post = (endpoint: string) => register(ServerMethod.POST, endpoint)

export const Put = (endpoint: string) => register(ServerMethod.PUT, endpoint)

export const Patch = (endpoint: string) =>
  register(ServerMethod.PATCH, endpoint)

export const Delete = (endpoint: string) =>
  register(ServerMethod.DELETE, endpoint)
