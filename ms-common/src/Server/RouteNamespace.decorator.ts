import { Debug } from '@thrift/common/engines/Debug'
import { Server, ServerMethod } from '@thrift/common/engines/Server'
import { RouteRegistry } from '@thrift/common/engines/Server/RouteRegistry'

interface RouteNamespaceConfig {
  prefix?: string
  priority?: number
  conflictStrategy?: 'ERROR' | 'WARN' | 'IGNORE'
}

const routeNamespaces = new Map<string, RouteNamespaceConfig>()

export const RouteNamespace = (config: RouteNamespaceConfig) => {
  return <T extends { new (...args: any[]): {} }>(constructor: T) => {
    const className = constructor.name
    routeNamespaces.set(className, config)
    
    Debug.info({
      message: `Route namespace configured for ${className}`,
      details: config
    })
    
    return constructor
  }
}

const registerWithNamespace = (method: ServerMethod, endpoint: string) => {
  return (
    target: object,
    propertyKey: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    descriptor: TypedPropertyDescriptor<any>,
  ) => {
    const className = target.constructor.name
    const config = routeNamespaces.get(className)
    
    // Apply namespace prefix if configured
    let finalEndpoint = endpoint
    if (config?.prefix) {
      finalEndpoint = `${config.prefix}${endpoint}`
    }
    
    // Extract file and class information for conflict detection
    const fileName = target.constructor.name || 'Unknown'
    const methodName = propertyKey

    // Register route with conflict detection
    RouteRegistry.registerRoute(method, finalEndpoint, fileName, className, methodName)

    Server.router[method]({
      endpoint: finalEndpoint,
      callback: (props) => descriptor.value(props),
    })

    Debug.info({
      message: `"${method}:${finalEndpoint}" has been routing successfully (namespace: ${className})`,
    })
  }
}

// Enhanced decorators with namespace support
export const NamespacedGet = (endpoint: string) => registerWithNamespace(ServerMethod.GET, endpoint)
export const NamespacedPost = (endpoint: string) => registerWithNamespace(ServerMethod.POST, endpoint)
export const NamespacedPut = (endpoint: string) => registerWithNamespace(ServerMethod.PUT, endpoint)
export const NamespacedPatch = (endpoint: string) => registerWithNamespace(ServerMethod.PATCH, endpoint)
export const NamespacedDelete = (endpoint: string) => registerWithNamespace(ServerMethod.DELETE, endpoint)

// Priority-based route registration
export class PriorityRouteManager {
  private static pendingRoutes: Array<{
    priority: number
    register: () => void
  }> = []

  public static addRoute(priority: number, registerFn: () => void): void {
    this.pendingRoutes.push({ priority, register: registerFn })
  }

  public static registerAllByPriority(): void {
    // Sort by priority (higher numbers first)
    this.pendingRoutes.sort((a, b) => b.priority - a.priority)
    
    // Register routes in priority order
    this.pendingRoutes.forEach(route => route.register())
    
    // Clear pending routes
    this.pendingRoutes = []
    
    Debug.info({
      message: `Registered ${this.pendingRoutes.length} routes by priority order`
    })
  }
}

// Priority-aware decorator
export const PriorityRoute = (method: ServerMethod, endpoint: string, priority: number = 0) => {
  return (
    target: object,
    propertyKey: string,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    descriptor: TypedPropertyDescriptor<any>,
  ) => {
    const registerFn = () => {
      const className = target.constructor.name
      const fileName = target.constructor.name || 'Unknown'
      const methodName = propertyKey

      RouteRegistry.registerRoute(method, endpoint, fileName, className, methodName)

      Server.router[method]({
        endpoint,
        callback: (props) => descriptor.value(props),
      })

      Debug.info({
        message: `"${method}:${endpoint}" registered with priority ${priority}`,
      })
    }

    PriorityRouteManager.addRoute(priority, registerFn)
  }
}

// Convenience decorators with priority
export const HighPriorityGet = (endpoint: string) => PriorityRoute(ServerMethod.GET, endpoint, 100)
export const LowPriorityGet = (endpoint: string) => PriorityRoute(ServerMethod.GET, endpoint, -100)
