import { Debug } from '@thrift/common/engines/Debug'
import { ServerMethod } from '@thrift/common/engines/Server'

export interface RouteInfo {
  method: ServerMethod
  endpoint: string
  fileName: string
  className: string
  methodName: string
  registrationOrder: number
}

export interface RouteConflict {
  conflictType: 'PARAMETER_OVERSHADOWING' | 'DUPLICATE_ROUTE' | 'AMBIGUOUS_PATTERN'
  affectedRoutes: RouteInfo[]
  description: string
  severity: 'ERROR' | 'WARNING'
  suggestion: string
}

export class RouteRegistry {
  private static routes: Map<string, RouteInfo[]> = new Map()
  private static registrationCounter = 0
  private static conflicts: RouteConflict[] = []

  public static registerRoute(
    method: ServerMethod,
    endpoint: string,
    fileName: string,
    className: string,
    methodName: string
  ): void {
    const routeKey = `${method}:${endpoint}`
    const routeInfo: RouteInfo = {
      method,
      endpoint,
      fileName,
      className,
      methodName,
      registrationOrder: ++this.registrationCounter
    }

    // Store route
    if (!this.routes.has(routeKey)) {
      this.routes.set(routeKey, [])
    }
    this.routes.get(routeKey)!.push(routeInfo)

    // Check for conflicts
    this.detectConflicts(routeInfo)

    Debug.info({
      message: `Route registered: ${routeKey} (${fileName}:${className}.${methodName})`
    })
  }

  private static detectConflicts(newRoute: RouteInfo): void {
    const methodRoutes = Array.from(this.routes.entries())
      .filter(([key]) => key.startsWith(`${newRoute.method}:`))
      .map(([, routes]) => routes)
      .flat()

    // Check for parameter overshadowing
    this.checkParameterOvershadowing(newRoute, methodRoutes)
    
    // Check for duplicate routes
    this.checkDuplicateRoutes(newRoute, methodRoutes)
    
    // Check for ambiguous patterns
    this.checkAmbiguousPatterns(newRoute, methodRoutes)
  }

  private static checkParameterOvershadowing(
    newRoute: RouteInfo,
    existingRoutes: RouteInfo[]
  ): void {
    const newSegments = this.parseRoute(newRoute.endpoint)
    
    for (const existingRoute of existingRoutes) {
      if (existingRoute.registrationOrder >= newRoute.registrationOrder) continue
      
      const existingSegments = this.parseRoute(existingRoute.endpoint)
      
      // Check if parameterized route overshadows specific route
      if (this.isParameterOvershadowing(existingSegments, newSegments)) {
        const conflict: RouteConflict = {
          conflictType: 'PARAMETER_OVERSHADOWING',
          affectedRoutes: [existingRoute, newRoute],
          description: `Route "${newRoute.endpoint}" will never be reached because "${existingRoute.endpoint}" (registered earlier) captures all requests`,
          severity: 'ERROR',
          suggestion: `Move "${newRoute.endpoint}" before "${existingRoute.endpoint}" in registration order, or place them in the same file with specific routes first`
        }
        
        this.conflicts.push(conflict)
        this.logConflict(conflict)
      }
    }
  }

  private static checkDuplicateRoutes(
    newRoute: RouteInfo,
    existingRoutes: RouteInfo[]
  ): void {
    const duplicates = existingRoutes.filter(
      route => route.endpoint === newRoute.endpoint && 
               route.registrationOrder < newRoute.registrationOrder
    )

    if (duplicates.length > 0) {
      const conflict: RouteConflict = {
        conflictType: 'DUPLICATE_ROUTE',
        affectedRoutes: [duplicates[0], newRoute],
        description: `Duplicate route "${newRoute.endpoint}" found`,
        severity: 'ERROR',
        suggestion: 'Remove duplicate route definition or use different endpoints'
      }
      
      this.conflicts.push(conflict)
      this.logConflict(conflict)
    }
  }

  private static checkAmbiguousPatterns(
    newRoute: RouteInfo,
    existingRoutes: RouteInfo[]
  ): void {
    const newSegments = this.parseRoute(newRoute.endpoint)
    
    for (const existingRoute of existingRoutes) {
      if (existingRoute.registrationOrder >= newRoute.registrationOrder) continue
      
      const existingSegments = this.parseRoute(existingRoute.endpoint)
      
      if (this.areRoutesAmbiguous(existingSegments, newSegments)) {
        const conflict: RouteConflict = {
          conflictType: 'AMBIGUOUS_PATTERN',
          affectedRoutes: [existingRoute, newRoute],
          description: `Routes "${existingRoute.endpoint}" and "${newRoute.endpoint}" have ambiguous patterns`,
          severity: 'WARNING',
          suggestion: 'Consider using more specific route patterns or different parameter names'
        }
        
        this.conflicts.push(conflict)
        this.logConflict(conflict)
      }
    }
  }

  private static parseRoute(endpoint: string): string[] {
    return endpoint.split('/').filter(segment => segment.length > 0)
  }

  private static isParameterOvershadowing(
    paramRoute: string[],
    specificRoute: string[]
  ): boolean {
    if (paramRoute.length !== specificRoute.length) return false
    
    let hasParam = false
    let hasSpecific = false
    
    for (let i = 0; i < paramRoute.length; i++) {
      const paramSegment = paramRoute[i]
      const specificSegment = specificRoute[i]
      
      if (paramSegment.startsWith(':')) {
        hasParam = true
      } else if (paramSegment !== specificSegment) {
        return false
      }
      
      if (!specificSegment.startsWith(':')) {
        hasSpecific = true
      }
    }
    
    return hasParam && hasSpecific
  }

  private static areRoutesAmbiguous(route1: string[], route2: string[]): boolean {
    if (route1.length !== route2.length) return false
    
    for (let i = 0; i < route1.length; i++) {
      const seg1 = route1[i]
      const seg2 = route2[i]
      
      if (seg1.startsWith(':') && seg2.startsWith(':')) {
        continue // Both parameters, potentially ambiguous
      } else if (seg1 !== seg2) {
        return false
      }
    }
    
    return true
  }

  private static logConflict(conflict: RouteConflict): void {
    const logMethod = conflict.severity === 'ERROR' ? Debug.error : Debug.warn
    
    logMethod({
      message: `ROUTE CONFLICT [${conflict.severity}]: ${conflict.description}`,
      details: {
        type: conflict.conflictType,
        routes: conflict.affectedRoutes.map(route => ({
          endpoint: route.endpoint,
          file: route.fileName,
          class: route.className,
          method: route.methodName,
          order: route.registrationOrder
        })),
        suggestion: conflict.suggestion
      }
    })
  }

  public static getConflicts(): RouteConflict[] {
    return [...this.conflicts]
  }

  public static getRoutes(): Map<string, RouteInfo[]> {
    return new Map(this.routes)
  }

  public static generateConflictReport(): string {
    if (this.conflicts.length === 0) {
      return 'No route conflicts detected.'
    }

    let report = `\n=== ROUTE CONFLICT REPORT ===\n`
    report += `Total conflicts found: ${this.conflicts.length}\n\n`

    this.conflicts.forEach((conflict, index) => {
      report += `${index + 1}. ${conflict.conflictType} [${conflict.severity}]\n`
      report += `   Description: ${conflict.description}\n`
      report += `   Affected routes:\n`
      
      conflict.affectedRoutes.forEach(route => {
        report += `     - ${route.method.toUpperCase()} ${route.endpoint} (${route.fileName}:${route.className}.${route.methodName})\n`
      })
      
      report += `   Suggestion: ${conflict.suggestion}\n\n`
    })

    return report
  }
}
