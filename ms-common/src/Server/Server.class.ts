import express from 'express'
import { resolve } from 'path'

import { Debug } from '@thrift/common/engines/Debug'
import { requestContextMiddleware } from '@thrift/common/engines/Http'
import {
  prepareRequest,
  prepareResponse,
  registerCredentials,
  triggerResources,
} from '@thrift/common/engines/Resource'
import {
  ServerMethod,
  type ServerRouterMethodProps,
  type ServerRouterMethods,
  type ServerStartMethodProps,
} from '@thrift/common/engines/Server'
import { RouteRegistry } from '@thrift/common/engines/Server/RouteRegistry'

export class Server {
  private static readonly serverApp: express.Express = express()
  private static readonly serverRouter: express.Router = express.Router()

  static get router(): ServerRouterMethods {
    return Object.values(ServerMethod).reduce(
      (result: ServerRouterMethods, method) => ({
        ...result,
        [method]: ({ endpoint, callback }: ServerRouterMethodProps) =>
          Server.serverRouter[method](
            endpoint,
            (request: express.Request, response: express.Response) =>
              callback({
                request: prepareRequest(request),
                response: prepareResponse(response),
              }),
          ),
      }),
      {} as ServerRouterMethods,
    )
  }

  public static start({ pathRoot, host, port }: ServerStartMethodProps): void {
    Debug.info({ message: 'Press CTRL+C to stop the server' })

    triggerResources(resolve(pathRoot, './src/resources'))

    // Generate and log route conflict report
    const conflictReport = RouteRegistry.generateConflictReport()
    const conflicts = RouteRegistry.getConflicts()

    if (conflicts.length > 0) {
      Debug.warn({ message: conflictReport })

      // Exit with error if there are ERROR-level conflicts
      const errorConflicts = conflicts.filter(c => c.severity === 'ERROR')
      if (errorConflicts.length > 0) {
        Debug.error({
          message: `Server startup aborted due to ${errorConflicts.length} critical route conflict(s)`
        })
        process.exit(1)
      }
    } else {
      Debug.info({ message: 'Route conflict check passed - no conflicts detected' })
    }

    Server.serverApp.use([
      express.json(),
      requestContextMiddleware,
      registerCredentials(resolve(pathRoot, './.credentials')),
      Server.serverRouter,
    ])

    Server.serverApp.disable('x-powered-by')
    Server.serverApp.listen(port, (error: Error | undefined) => {
      if (error) {
        Debug.error({ message: error.message })
      } else {
        Debug.info({
          message: `The server is running on http://${host}:${port}`,
        })
      }
    })
  }
}
