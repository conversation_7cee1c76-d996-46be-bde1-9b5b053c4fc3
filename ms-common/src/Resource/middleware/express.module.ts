import type { Request, Response } from 'express'
import { readdir } from 'fs'
import { resolve, basename } from 'path'

import { Debug } from '@thrift/common/engines/Debug'
import {
  ResourceMessageCode,
  type ResourceRequest,
  type ResourceResponse,
  type ResourceResponseSendProps,
} from '@thrift/common/engines/Resource'
import { RouteOrderManager } from '@thrift/common/engines/Resource/RouteOrderConfig'

export const prepareRequest = (request: Request): ResourceRequest => ({
  body: <B = unknown>() => request.body as B,
  headers: <H = unknown>() => request.headers as H,
  params: <P = unknown>() => request.params as P,
  query: <Q = unknown>() => request.query as Q,
  url: <Q = unknown>() => request.url as Q,
})

export const prepareResponse = (response: Response): ResourceResponse => ({
  send: <T>({ code, message, data }: ResourceResponseSendProps<T>) => {
    return response.status(Number(code.split('_')[1])).json({
      code,
      message,
      data: data || null,
    })
  },
})

export const triggerResources = (resourcePathName: string): void => {
  readdir(resourcePathName, { withFileTypes: true }, (error, files) => {
    if (error) {
      Debug.error({ message: error.message })
    } else {
      // Separate directories and resource files
      const directories = files.filter(file => file.isDirectory())
      const resourceFiles = files.filter(file =>
        file.isFile() && file.name.match(/^(.*)(\.resource\.)(js|ts)$/)
      )

      // Process directories recursively first
      directories.forEach((dir) => {
        triggerResources(resolve(resourcePathName, dir.name))
      })

      // Sort resource files by priority to avoid conflicts
      const directoryName = basename(resourcePathName)
      const sortedFiles = RouteOrderManager.sortResourceFiles(
        resourceFiles.map(f => f.name),
        directoryName
      )

      // Import resource files in sorted order
      sortedFiles.forEach((fileName) => {
        import(resolve(resourcePathName, fileName)).catch((error) => {
          Debug.error({ message: error.message })
        })
      })
    }
  })
}

export const registerCredentials =
  (credentialsPathName: string) => (request, response, next) =>
    import(resolve(credentialsPathName, 'authorization'))
      .then((module) =>
        module.authorization({
          request: prepareRequest(request),
          response: prepareResponse(response),
          next,
        }),
      )
      .catch((error) => {
        Debug.error({ message: error.message })

        response.send({
          code: ResourceMessageCode.C_401_0401,
          message: 'Unauthorized not found credentials',
        })

        next(error)
      })
