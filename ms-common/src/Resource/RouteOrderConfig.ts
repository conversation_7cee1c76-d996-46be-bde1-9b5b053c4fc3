export interface RouteOrderConfig {
  // Files to load first (highest priority)
  priorityFiles: string[]
  
  // Files to load last (lowest priority)
  deferredFiles: string[]
  
  // Custom ordering rules
  customOrder?: {
    [directory: string]: string[]
  }
}

export const defaultRouteOrderConfig: RouteOrderConfig = {
  // Load specific routes before parameterized routes
  priorityFiles: [
    'PersonLegal.resource.ts',
    'PersonNatural.resource.ts',
    'StoreCredit.resource.ts'
  ],
  
  // Load parameterized routes last
  deferredFiles: [
    'Person.resource.ts'
  ],
  
  // Directory-specific ordering
  customOrder: {
    'Person': [
      'PersonLegal.resource.ts',
      'PersonNatural.resource.ts', 
      'Person.resource.ts'
    ]
  }
}

export class RouteOrderManager {
  public static sortResourceFiles(
    files: string[], 
    directory: string,
    config: RouteOrderConfig = defaultRouteOrderConfig
  ): string[] {
    // Check for custom directory ordering first
    if (config.customOrder?.[directory]) {
      return this.applySortOrder(files, config.customOrder[directory])
    }

    // Apply global priority/deferred ordering
    const priorityFiles = files.filter(file => 
      config.priorityFiles.some(priority => file.includes(priority))
    )
    
    const deferredFiles = files.filter(file => 
      config.deferredFiles.some(deferred => file.includes(deferred))
    )
    
    const regularFiles = files.filter(file => 
      !priorityFiles.includes(file) && !deferredFiles.includes(file)
    )

    return [...priorityFiles, ...regularFiles, ...deferredFiles]
  }

  private static applySortOrder(files: string[], order: string[]): string[] {
    const sortedFiles: string[] = []
    const remainingFiles = [...files]

    // Add files in specified order
    for (const orderFile of order) {
      const index = remainingFiles.findIndex(file => file.includes(orderFile))
      if (index !== -1) {
        sortedFiles.push(remainingFiles.splice(index, 1)[0])
      }
    }

    // Add any remaining files
    sortedFiles.push(...remainingFiles)

    return sortedFiles
  }
}
