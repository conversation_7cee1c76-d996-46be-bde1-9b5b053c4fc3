import { Language } from '@app/application/Language'
import { Person, type PersonNaturalResponse } from '@app/application/Person'
import {
  CreatePersonNaturalSchema,
  UpdatePersonNaturalSchema,
} from '@app/application/Person/PersonNatural.schema'

import { PersonNatural as PersonNaturalDatabase } from '@app/domain/database/PersonNatural'

export class PersonNatural {
  public async create(input: unknown): Promise<PersonNaturalResponse> {
    const dto = CreatePersonNaturalSchema.parse(input)

    const model = new PersonNaturalDatabase()
    const personApp = new Person()

    const person = await personApp.create({ name: dto.name })

    const personNatural = await model.create({
      birthDate: dto.birthDate,
      gender: dto.gender,
      name: dto.name,
      personId: person.id,
    })

    return personNatural
  }

  public async findAll(): Promise<PersonNaturalResponse[]> {
    const model = new PersonNaturalDatabase()

    const personNatural = await model.findAll()

    return personNatural as unknown as PersonNaturalResponse[]
  }

  public async findById(findId: string): Promise<PersonNaturalResponse> {
    const model = new PersonNaturalDatabase()

    const { id, personId, birthDate, gender, name, createdAt, updatedAt } =
      await model.findById(findId)

    return {
      id,
      personId,
      birthDate,
      gender,
      name,
      createdAt,
      updatedAt,
    } as unknown as PersonNaturalResponse
  }

  public async findByPersonId(
    findPersonId: string,
  ): Promise<Omit<PersonNaturalResponse, 'deleted'>> {
    const model = new PersonNaturalDatabase()

    const { id, personId, birthDate, gender, name, createdAt, updatedAt } =
      await model.findByPersonId(findPersonId)

    return {
      id,
      personId,
      birthDate,
      gender,
      name,
      createdAt,
      updatedAt,
    } as unknown as PersonNaturalResponse
  }

  public async update(input: unknown): Promise<PersonNaturalResponse> {
    const dto = UpdatePersonNaturalSchema.parse(input)

    const model = new PersonNaturalDatabase()
    const personApp = new Person()

    // Get existing record to merge with updates
    const existingRecord = await model.findById(dto.id)

    if (dto.name && dto.personId) {
      await personApp.update({
        id: dto.personId,
        name: dto.name,
      })
    }

    const personNatural = await model.update({
      id: dto.id,
      birthDate: dto.birthDate ?? existingRecord.birthDate,
      gender: dto.gender ?? existingRecord.gender,
      name: dto.name ?? existingRecord.name,
      personId: dto.personId ?? existingRecord.personId,
    })

    return personNatural
  }

  public async delete(id: string) {
    const model = new PersonNaturalDatabase()

    const existingItem = await model.findById(id)

    if (!existingItem) {
      throw new ReferenceError(Language.translate('person:notFound', { id }))
    }

    const personApp = new Person()

    await personApp.delete(existingItem.personId)

    return await model.delete(id)
  }
}
