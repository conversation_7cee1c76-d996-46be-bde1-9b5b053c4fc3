import { Debug } from '@thrift/common/engines/Debug'
import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  type IdentifierDto,
  PersonLegal as PersonLegalApplication,
  type PersonLegalResponse,
  type PersonRequest,
} from '@app/application/Person'

export class Person {
  @Post('/person-legal')
  public async create({ request, response }: ResourceMethodProps) {
    try {
      response.send<PersonLegalResponse>({
        code: ResourceMessageCode.C_201_0201,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_201_0201}`,
        ),
        data: await new PersonLegalApplication().create(request.body()),
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      if (error instanceof ReferenceError) {
        response.send<string>({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('person:notFound', { id: 'body' }),
        })
      } else if (error instanceof Error) {
        response.send<string>({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resource:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Get('/person-legal')
  public async findAll({ response }: ResourceMethodProps) {
    try {
      response.send<PersonLegalResponse[]>({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonLegalApplication().findAll(),
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      if (error instanceof ReferenceError) {
        response.send<string>({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('person:notFoundAll'),
        })
      } else if (error instanceof Error) {
        response.send<string>({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resource:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Get('/person-legal/:id')
  public async findById({ request, response }: ResourceMethodProps) {
    const { id } = request.params<IdentifierDto>()

    try {
      response.send<PersonLegalResponse>({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonLegalApplication().findById(id),
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      if (error instanceof ReferenceError) {
        response.send<string>({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('person:notFound', {
            id,
          }),
        })
      } else if (error instanceof Error) {
        response.send<string>({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resource:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Get('/person-legal/personId/:personId')
  public async findByPersonId({ request, response }: ResourceMethodProps) {
    const { personId } = request.params<PersonRequest>()

    try {
      response.send<PersonLegalResponse>({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonLegalApplication().findByPersonId(personId),
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      if (error instanceof ReferenceError) {
        response.send<string>({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('person:notFound', {
            id: request.params<PersonRequest>().personId,
          }),
        })
      } else if (error instanceof Error) {
        response.send<string>({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resource:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Put('/person-legal/:personId')
  public async update({ request, response }: ResourceMethodProps) {
    const { personId } = request.params<PersonRequest>()

    try {
      response.send<PersonLegalResponse>({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonLegalApplication().update({
          ...request.body(),
          id: personId,
        }),
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      if (error instanceof ReferenceError) {
        response.send<string>({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('person:notFound', { id: personId }),
        })
      } else if (error instanceof Error) {
        response.send<string>({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resource:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }

  @Delete('/person-legal/:id')
  public async delete({ request, response }: ResourceMethodProps) {
    const { id } = request.params<IdentifierDto>()

    try {
      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonLegalApplication().delete(id),
      })
    } catch (error) {
      Debug.error<Error>({ message: error })

      if (error instanceof ReferenceError) {
        response.send<string>({
          code: ResourceMessageCode.C_400_0400,
          message: Language.translate('person:notFound', { id }),
        })
      } else if (error instanceof Error) {
        response.send<string>({
          code: ResourceMessageCode.C_500_0500,
          message: Language.translate(
            `resource:${ResourceMessageCode.C_500_0500}`,
          ),
        })
      }
    }
  }
}
